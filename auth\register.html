<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - <PERSON></title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicon.png">

    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        
        .page-header {
            text-align: center;
            margin: 120px 0 30px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .register-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 50px;
            align-items: stretch;
        }
        
        .register-form-container {
            flex: 1;
            min-width: 320px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            position: relative;
            overflow: hidden;
        }
        
        .register-form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #4285F4, #ff7aa8);
        }
        
        .form-header {
            margin-bottom: 25px;
            text-align: center;
        }
        
        .form-header h2 {
            color: #4285F4;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .form-header p {
            color: #666;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: #4285F4;
            outline: none;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        
        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .register-btn {
            background: linear-gradient(to right, #4285F4, #ff7aa8);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.4);
        }
        
        .rules-container {
            flex: 1;
            min-width: 320px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 600px;
        }
        
        .rules-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #ff7aa8, #4285F4);
        }
        
        .rules-header {
            margin-bottom: 25px;
            text-align: center;
        }
        
        .rules-header {
            flex-shrink: 0;
            margin-bottom: 15px;
        }

        .rules-header h2 {
            color: #ff7aa8;
            font-size: 1.8rem;
            margin-bottom: 0;
        }
        
        .rules-content {
            flex: 1;
            max-height: 900px;
            overflow-y: auto;
            padding-right: 15px;
            padding-bottom: 20px;
        }
        
        .rules-content::-webkit-scrollbar {
            width: 8px;
        }

        .rules-content::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 10px;
            margin: 5px 0;
        }

        .rules-content::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #4285F4, #ff7aa8);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .rules-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, #3367D6, #e55a8a);
            transform: scale(1.1);
        }
        
        .rules-content h2 {
            color: #4285F4;
            font-size: 1.4rem;
            margin: 0 0 15px;
            text-align: center;
        }

        .rules-content h3 {
            color: #4285F4;
            font-size: 1.1rem;
            margin: 20px 0 10px;
        }
        
        .rules-content h3:first-child {
            margin-top: 0;
        }
        
        .rules-content p {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .rules-content ol {
            padding-left: 20px;
            margin-bottom: 15px;
        }
        
        .rules-content li {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .rules-content li ol {
            list-style-type: lower-alpha;
            margin: 10px 0;
        }
        
        .contact-info {
            margin-top: 40px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
        }
        
        .contact-info h2 {
            color: #4285F4;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .contact-methods {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }
        
        .contact-method {
            flex: 1;
            min-width: 200px;
            max-width: 250px;
            padding: 20px;
            border-radius: 10px;
            background-color: #f8f9fa;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .contact-method:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        .contact-method i {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #ff7aa8;
        }
        
        .contact-method h3 {
            margin-bottom: 10px;
            font-size: 1.2rem;
            color: #333;
        }
        
        .contact-method p,
        .contact-method a {
            color: #666;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .contact-method a:hover {
            color: #4285F4;
        }
        
        .agreement-checkbox {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        
        .agreement-checkbox input {
            margin-top: 5px;
            margin-right: 10px;
        }
        
        .success-message {
            background-color: rgba(76, 217, 100, 0.1);
            color: #2ca745;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            font-weight: 500;
            animation: fadeIn 0.5s ease-out forwards;
            display: none;
        }

        .error-message {
            background-color: rgba(255, 77, 77, 0.1);
            color: #ff4d4d;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            font-weight: 500;
            animation: fadeIn 0.5s ease-out forwards;
            display: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .loading i {
            color: #4285F4;
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .register-container {
                flex-direction: column;
            }
            
            .contact-methods {
                flex-direction: column;
                align-items: center;
            }
            
            .contact-method {
                width: 100%;
                max-width: 100%;
            }
        }
        
        /* Hiệu ứng animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .register-form-container, .rules-container, .contact-info {
            animation: fadeIn 0.8s ease-out forwards;
        }
        
        .rules-container {
            animation-delay: 0.2s;
        }
        
        .contact-info {
            animation-delay: 0.4s;
            opacity: 0;
        }
        
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="register.html" class="active">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="index.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Register Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Đăng Ký Khóa Học</h1>
                <p>Vui lòng điền đầy đủ thông tin để đăng ký khóa học. Sau khi đăng ký, chúng tôi sẽ liên hệ với bạn để cung cấp thông tin đăng nhập.</p>
            </div>
            
            <div class="register-container">
                <div class="register-form-container">
                    <div class="form-header">
                        <h2>Thông Tin Đăng Ký</h2>
                        <p>Các thông tin có dấu <span style="color: #ff0000;">*</span> là bắt buộc</p>
                    </div>
                    
                    <form id="registrationForm">
                        <div class="form-group">
                            <label for="fullname">Họ và tên <span style="color: #ff0000;">*</span></label>
                            <input type="text" id="fullname" name="fullname" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="birthdate">Ngày sinh <span style="color: #ff0000;">*</span></label>
                            <input type="date" id="birthdate" name="birthdate" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">Số điện thoại <span style="color: #ff0000;">*</span></label>
                            <input type="tel" id="phone" name="phone" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email <span style="color: #ff0000;">*</span></label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="class">Lớp học quan tâm <span style="color: #ff0000;">*</span></label>
                            <select id="class" name="class" required>
                                <option value="">-- Chọn lớp học --</option>
                                <option value="python-a">Python - A (Thứ 7 - Chủ Nhật, 19:30 - 21:00)</option>
                                <option value="python-b">Python - B (Thứ 2 - Thứ 4, 19:30 - 21:00)</option>
                                <option value="python-c">Python - C (Thứ 3 - Thứ 5, 19:30 - 21:00)</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="experience">Kinh nghiệm lập trình</label>
                            <select id="experience" name="experience">
                                <option value="none">Chưa có kinh nghiệm</option>
                                <option value="beginner">Mới bắt đầu</option>
                                <option value="intermediate">Đã học qua</option>
                                <option value="advanced">Thành thạo</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="expectation">Mong đợi từ khóa học</label>
                            <textarea id="expectation" name="expectation"></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="register-btn">Gửi Đăng Ký</button>
                        </div>
                    </form>
                    
                    <div class="success-message" id="successMessage" style="display:none;">
                        Đăng ký thành công! Chúng tôi sẽ liên hệ với bạn sớm để xác nhận và cung cấp thông tin đăng nhập.
                    </div>
                    
                    <div class="error-message" id="errorMessage" style="display:none;">
                        Có lỗi xảy ra. Vui lòng thử lại sau.
                    </div>
                    
                    <div class="loading" id="loadingState" style="display:none;">
                        <i class="fas fa-spinner"></i> Đang xử lý...
                    </div>
                </div>
                
                <div class="rules-container">
                    <div class="rules-header">
                        <h2>Nội Quy Lớp Học</h2>
                    </div>
                    
                    <div class="rules-content">
                        <h3>NỘI QUY LỚP HỌC PYTHON VÀ AI ONLINE</h3>
                        <p>(Giáo viên: Lê Quang Vinh)</p>
                        <p>Để đảm bảo một môi trường học tập trực tuyến hiệu quả, tích cực và tôn trọng lẫn nhau, tất cả học viên vui lòng tuân thủ các nội quy sau:</p>
                        
                        <ol>
                            <li>
                                <strong>Chuẩn bị trước buổi học:</strong>
                                <ul>
                                    <li>Xem lại bài cũ và chuẩn bị sẵn sàng công cụ học tập (máy tính đã cài đặt Python, VS Code, kết nối Internet ổn định).</li>
                                    <li>Chọn không gian học tập yên tĩnh, đủ ánh sáng để tập trung cao nhất.</li>
                                </ul>
                            </li>

                            <li>
                                <strong>Giờ giấc và Tham gia lớp học:</strong>
                                <ul>
                                    <li>Vào lớp đúng giờ. Việc vào lớp muộn có thể ảnh hưởng đến tiến độ chung và sự tập trung của cả lớp.</li>
                                    <li>Nếu có việc đột xuất cần vào lớp muộn hoặc nghỉ học, học viên (hoặc phụ huynh) vui lòng nhắn tin xin phép giáo viên TRƯỚC giờ học ít nhất 15 phút (trừ trường hợp bất khả kháng).</li>
                                </ul>
                            </li>

                            <li>
                                <strong>Tương tác trong lớp:</strong>
                                <ul>
                                    <li>Luôn bật camera trong suốt buổi học (trừ trường hợp có lý do đặc biệt và đã báo trước cho giáo viên). Điều này giúp tăng cường sự tương tác và giúp giáo viên dễ dàng theo dõi mức độ hiểu bài của học viên.</li>
                                    <li>Mở microphone khi phát biểu, trả lời câu hỏi hoặc khi giáo viên yêu cầu. Trong thời gian còn lại, vui lòng tắt microphone để tránh tiếng ồn làm ảnh hưởng lớp học.</li>
                                    <li>Sẵn sàng chia sẻ màn hình khi giáo viên yêu cầu để được hỗ trợ gỡ lỗi hoặc trình bày bài làm.</li>
                                </ul>
                            </li>

                            <li>
                                <strong>Bài tập về nhà:</strong>
                                <ul>
                                    <li>Hoàn thành đầy đủ và đúng hạn bài tập được giao về nhà. Đây là phần quan trọng để củng cố kiến thức đã học.</li>
                                    <li>Nếu không thể hoàn thành bài tập vì lý do chính đáng, cần thông báo và giải thích với giáo viên.</li>
                                </ul>
                            </li>

                            <li>
                                <strong>Thái độ học tập:</strong>
                                <ul>
                                    <li>Tập trung nghe giảng, tích cực tham gia thảo luận và đặt câu hỏi.</li>
                                    <li>Tôn trọng giáo viên và các bạn học viên khác. Không làm việc riêng, gây mất trật tự trong giờ học.</li>
                                    <li>Chủ động hỏi bài: Vì kiến thức mới và có thể khó, yêu cầu các học viên khi gặp vấn đề khó khăn hoặc chưa hiểu rõ, hãy hỏi ngay lập tức và chia sẻ với giáo viên để được giải đáp kịp thời.</li>
                                    <li>Chủ động ghi chép lại những kiến thức quan trọng.</li>
                                </ul>
                            </li>

                            <li>
                                <strong>Hỗ trợ dự án cá nhân/trường học:</strong>
                                <ul>
                                    <li>Giáo viên sẽ luôn nhiệt tình hỗ trợ, tư vấn và định hướng cho học sinh trong các dự án, cuộc thi nghiên cứu khoa học liên quan đến lập trình/AI được tổ chức ở trường.</li>
                                    <li>Tuy nhiên, học sinh cần có tinh thần tự giác cao, chủ động tìm tòi, nghiên cứu và nỗ lực hoàn thành các công việc của bản thân.</li>
                                </ul>
                            </li>

                            <li>
                                <strong>Học phí:</strong>
                                <ul>
                                    <li>Học phí: 250.000 VNĐ/tháng (bao gồm 8 buổi học).</li>
                                    <li>Vui lòng hoàn thành học phí sau mỗi 4 buổi học đầu tiên của mỗi tháng để đảm bảo việc học được liên tục. Hình thức thanh toán sẽ được giáo viên thông báo cụ thể.</li>
                                </ul>
                            </li>

                            <li>
                                <strong>Liên lạc:</strong>
                                <ul>
                                    <li>Mọi thắc mắc về bài vở, lịch học hoặc các vấn đề khác, học viên/phụ huynh có thể liên hệ trực tiếp với giáo viên qua các kênh đã thống nhất.</li>
                                </ul>
                            </li>
                        </ol>

                        <p>Giáo viên và phụ huynh cùng nhau hợp tác để tạo điều kiện học tập tốt nhất cho các em. Chúc các em có những buổi học bổ ích và thú vị!</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container">
        <div class="account-notice" style="text-align: center; padding: 30px; background: #f8f9fa; border-radius: 10px; margin-bottom: 30px;">
            <h2 style="color: #4285F4; margin-bottom: 15px;">Lưu ý về tài khoản học viên</h2>
            <p style="margin-bottom: 10px;">Tài khoản học viên chỉ được tạo bởi quản trị viên sau khi bạn đăng ký khóa học và hoàn tất các thủ tục.</p>
            <p style="margin-bottom: 10px;">Thông tin đăng nhập sẽ được gửi trực tiếp đến bạn qua email hoặc số điện thoại đã đăng ký.</p>
            <p>Nếu bạn đã có tài khoản, vui lòng <a href="login.html" style="color: #4285F4; text-decoration: none;">đăng nhập tại đây</a>.</p>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import the functions you need from the SDKs
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-analytics.js";
        import { getFirestore, collection, addDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);
        
        // Handle form submission
        const form = document.getElementById('registrationForm');
        const successMessage = document.getElementById('successMessage');
        const errorMessage = document.getElementById('errorMessage');
        const loadingState = document.getElementById('loadingState');
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // Hide any previous messages
            successMessage.style.display = 'none';
            errorMessage.style.display = 'none';
            
            // Show loading state
            loadingState.style.display = 'block';
            
            try {
                // Prepare form data
                const formData = {
                    fullname: document.getElementById('fullname').value,
                    birthdate: document.getElementById('birthdate').value,
                    phone: document.getElementById('phone').value,
                    email: document.getElementById('email').value,
                    class: document.getElementById('class').value,
                    experience: document.getElementById('experience').value,
                    expectation: document.getElementById('expectation').value,
                    registrationDate: new Date().toISOString(),
                    status: 'pending' // pending, approved, rejected
                };
                
                // Submit to Firebase
                const docRef = await addDoc(collection(db, "registrations"), formData);
                console.log("Registration successful with ID: ", docRef.id);
                
                // Show success message
                successMessage.style.display = 'block';
                
                // Reset form
                form.reset();
            } catch (error) {
                console.error("Error processing registration: ", error);
                errorMessage.textContent = `Lỗi: ${error.message}`;
                errorMessage.style.display = 'block';
            } finally {
                // Hide loading state
                loadingState.style.display = 'none';
            }
        });
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html> 